import React, { useState, useEffect } from 'react';
import { Calendar, MapPin, Camera, User, CheckCircle, Trophy } from 'lucide-react';

interface Report {
  _id: string;
  category: string;
  description: string;
  image_url?: string;
  location?: { latitude: number; longitude: number };
  status: 'resolved';
  created_at: string;
  updated_at: string;
  userName: string;
}

const SolvedIssues: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    'Road Maintenance',
    'Streetlight Issues',
    'Waste Management',
    'Water Problems',
    'Park Maintenance',
    'Noise Complaints',
    'Building Violations',
    'Other',
  ];

  useEffect(() => {
    fetchSolvedReports();
  }, []);

  const fetchSolvedReports = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/reports/solved');
      
      if (!response.ok) {
        throw new Error('Failed to fetch solved reports');
      }

      const data = await response.json();
      setReports(data);
    } catch (err: any) {
      setError(err.message || 'Failed to load solved reports');
    } finally {
      setLoading(false);
    }
  };

  const filteredReports = reports.filter(report => {
    if (selectedCategory === 'all') return true;
    return report.category === selectedCategory;
  });

  const groupedReports = categories.reduce((acc, category) => {
    acc[category] = filteredReports.filter(report => report.category === category);
    return acc;
  }, {} as Record<string, Report[]>);

  if (loading) {
    return (
      <div className="container py-8">
        <div className="loading">
          <div className="spinner"></div>
          <p>Loading solved issues...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Trophy className="h-8 w-8 text-green-600" />
          <h1 className="text-3xl font-bold text-gray-900">Solved Issues</h1>
        </div>
        <p className="text-gray-600">
          Celebrate the community issues that have been successfully resolved
        </p>
        <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span className="font-semibold text-green-800">
              {reports.length} issues have been resolved!
            </span>
          </div>
        </div>
      </div>

      {error && (
        <div className="alert alert-error mb-6">
          <span>{error}</span>
        </div>
      )}

      {/* Category Filter */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedCategory === 'all'
                ? 'bg-green-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            All Solved ({reports.length})
          </button>
          {categories.map((category) => {
            const count = reports.filter(r => r.category === category).length;
            if (count === 0) return null;
            
            return (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {category} ({count})
              </button>
            );
          })}
        </div>
      </div>

      {/* Solved Issues by Category */}
      {selectedCategory === 'all' ? (
        <div className="space-y-12">
          {categories.map((category) => {
            const categoryReports = groupedReports[category];
            if (categoryReports.length === 0) return null;

            return (
              <div key={category} className="category-section">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                  {category}
                  <span className="bg-green-100 text-green-800 text-sm px-2 py-1 rounded-full">
                    {categoryReports.length} solved
                  </span>
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {categoryReports.map((report) => (
                    <SolvedReportCard key={report._id} report={report} />
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredReports.map((report) => (
            <SolvedReportCard key={report._id} report={report} />
          ))}
        </div>
      )}

      {filteredReports.length === 0 && (
        <div className="text-center py-12">
          <CheckCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No solved issues found</h3>
          <p className="text-gray-500">
            {selectedCategory === 'all' 
              ? 'No issues have been resolved yet' 
              : `No solved issues found in ${selectedCategory} category`}
          </p>
        </div>
      )}
    </div>
  );
};

const SolvedReportCard: React.FC<{ report: Report }> = ({ report }) => {
  return (
    <div className="card hover:shadow-lg transition-shadow border-l-4 border-green-500">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-lg font-semibold text-gray-900">{report.category}</h3>
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
              <CheckCircle className="h-4 w-4" />
              <span>Resolved</span>
            </span>
          </div>
          <p className="text-gray-700 mb-4 line-clamp-3">{report.description}</p>
          
          <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
            <div className="flex items-center gap-1">
              <User className="h-4 w-4" />
              <span>{report.userName}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>Reported: {new Date(report.created_at).toLocaleDateString()}</span>
            </div>
          </div>
          
          <div className="bg-green-50 p-2 rounded-lg">
            <div className="flex items-center gap-1 text-sm text-green-700">
              <CheckCircle className="h-4 w-4" />
              <span>Resolved on {new Date(report.updated_at).toLocaleDateString()}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-4 text-sm text-gray-500 mt-2">
            {report.location && (
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                <span>Located</span>
              </div>
            )}
            {report.image_url && (
              <div className="flex items-center gap-1">
                <Camera className="h-4 w-4" />
                <span>Photo</span>
              </div>
            )}
          </div>
        </div>

        {report.image_url && (
          <div className="ml-4">
            <img
              src={`http://localhost:5000${report.image_url}`}
              alt="Report"
              className="w-20 h-20 object-cover rounded-lg"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default SolvedIssues;
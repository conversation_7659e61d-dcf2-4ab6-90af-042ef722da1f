from flask import Flask, request, jsonify, send_from_directory
from flask_pymongo import PyMongo
from flask_jwt_extended import J<PERSON>TMana<PERSON>, jwt_required, create_access_token, get_jwt_identity
from flask_cors import CORS
from flask_bcrypt import Bcrypt
from flask_socketio import Socket<PERSON>, emit
from flask_socketio import Socket<PERSON>, emit
from flask_socketio import Socket<PERSON>, emit
from werkzeug.utils import secure_filename
from bson.objectid import ObjectId
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv
import uuid
import pymongo
import pymongo
import pymongo

load_dotenv()

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*")
socketio = SocketIO(app, cors_allowed_origins="*")
socketio = SocketIO(app, cors_allowed_origins="*")

# Configuration
app.config['MONGO_URI'] = 'mongodb://localhost:27017/civic_reporting'
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'civic-reporting-secret-key-2024')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize extensions
try:
    mongo = PyMongo(app)
    # Test connection and create database/collections if they don't exist
    mongo.db.command('ping')
    print("✅ Connected to MongoDB successfully!")
    
    # Create indexes for better performance
    mongo.db.users.create_index([("email", 1)], unique=True)
    mongo.db.reports.create_index([("user_id", 1)])
    mongo.db.reports.create_index([("status", 1)])
    mongo.db.reports.create_index([("category", 1)])
    mongo.db.reports.create_index([("created_at", -1)])
    
    # Create demo users if they don't exist
    if mongo.db.users.count_documents({}) == 0:
        from flask_bcrypt import Bcrypt
        bcrypt_instance = Bcrypt()
        
        demo_users = [
            {
                'name': 'Admin User',
                'email': '<EMAIL>',
                'password': bcrypt_instance.generate_password_hash('demo123').decode('utf-8'),
                'role': 'admin',
                'created_at': datetime.utcnow()
            },
            {
                'name': 'John Citizen',
                'email': '<EMAIL>',
                'password': bcrypt_instance.generate_password_hash('demo123').decode('utf-8'),
                'role': 'citizen',
                'created_at': datetime.utcnow()
            }
        ]
        mongo.db.users.insert_many(demo_users)
        print("✅ Demo users created successfully!")
        
except Exception as e:
    print(f"❌ MongoDB connection failed: {e}")
    print("Please ensure MongoDB is running on mongodb://localhost:27017/")

    mongo = PyMongo(app)
    # Test connection and create database/collections if they don't exist
    mongo.db.command('ping')
    print("✅ Connected to MongoDB successfully!")
    
    # Create indexes for better performance
    mongo.db.users.create_index([("email", 1)], unique=True)
    mongo.db.reports.create_index([("user_id", 1)])
    mongo.db.reports.create_index([("status", 1)])
    mongo.db.reports.create_index([("category", 1)])
    mongo.db.reports.create_index([("created_at", -1)])
    
    # Create demo users if they don't exist
    if mongo.db.users.count_documents({}) == 0:
        from flask_bcrypt import Bcrypt
        bcrypt_instance = Bcrypt()
        
        demo_users = [
            {
                'name': 'Admin User',
                'email': '<EMAIL>',
                'password': bcrypt_instance.generate_password_hash('demo123').decode('utf-8'),
                'role': 'admin',
                'created_at': datetime.utcnow()
            },
            {
                'name': 'John Citizen',
                'email': '<EMAIL>',
                'password': bcrypt_instance.generate_password_hash('demo123').decode('utf-8'),
                'role': 'citizen',
                'created_at': datetime.utcnow()
            }
        ]
        mongo.db.users.insert_many(demo_users)
        print("✅ Demo users created successfully!")
        
except Exception as e:
    print(f"❌ MongoDB connection failed: {e}")
    print("Please ensure MongoDB is running on mongodb://localhost:27017/")

    mongo = PyMongo(app)
    # Test connection and create database/collections if they don't exist
    mongo.db.command('ping')
    print("✅ Connected to MongoDB successfully!")
    
    # Create indexes for better performance
    mongo.db.users.create_index([("email", 1)], unique=True)
    mongo.db.reports.create_index([("user_id", 1)])
    mongo.db.reports.create_index([("status", 1)])
    mongo.db.reports.create_index([("category", 1)])
    mongo.db.reports.create_index([("created_at", -1)])
    
    # Create demo users if they don't exist
    if mongo.db.users.count_documents({}) == 0:
        from flask_bcrypt import Bcrypt
        bcrypt_instance = Bcrypt()
        
        demo_users = [
            {
                'name': 'Admin User',
                'email': '<EMAIL>',
                'password': bcrypt_instance.generate_password_hash('demo123').decode('utf-8'),
                'role': 'admin',
                'created_at': datetime.utcnow()
            },
            {
                'name': 'John Citizen',
                'email': '<EMAIL>',
                'password': bcrypt_instance.generate_password_hash('demo123').decode('utf-8'),
                'role': 'citizen',
                'created_at': datetime.utcnow()
            }
        ]
        mongo.db.users.insert_many(demo_users)
        print("✅ Demo users created successfully!")
        
except Exception as e:
    print(f"❌ MongoDB connection failed: {e}")
    print("Please ensure MongoDB is running on mongodb://localhost:27017/")

jwt = JWTManager(app)
bcrypt = Bcrypt(app)
CORS(app)

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def serialize_doc(doc):
    """Convert MongoDB document to JSON serializable format"""
    if doc is None:
        return None
    doc['_id'] = str(doc['_id'])
    return doc

# Routes
@app.route('/api/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        
        if not data or not all(k in data for k in ('name', 'email', 'password')):
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Check if user already exists
        if mongo.db.users.find_one({'email': data['email']}):
            return jsonify({'error': 'Email already registered'}), 400
        
        # Hash password
        password_hash = bcrypt.generate_password_hash(data['password']).decode('utf-8')
        
        # Create user document
        user_doc = {
            'name': data['name'],
            'email': data['email'],
            'password': password_hash,
            'role': data.get('role', 'citizen'),
            'created_at': datetime.utcnow()
        }
        
        # Insert user
        result = mongo.db.users.insert_one(user_doc)
        user_id = str(result.inserted_id)
        
        # Create access token
        access_token = create_access_token(
            identity=user_id,
            additional_claims={
                'email': data['email'],
                'role': data.get('role', 'citizen')
            }
        )
        
        return jsonify({
            'token': access_token,
            'user': {
                'id': user_id,
                'name': data['name'],
                'email': data['email'],
                'role': data.get('role', 'citizen')
            }
        }), 201
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        
        if not data or not all(k in data for k in ('email', 'password')):
            return jsonify({'error': 'Email and password required'}), 400
        
        # Find user
        user = mongo.db.users.find_one({'email': data['email']})
        if not user or not bcrypt.check_password_hash(user['password'], data['password']):
            return jsonify({'error': 'Invalid credentials'}), 401
        
        # Create access token
        access_token = create_access_token(
            identity=str(user['_id']),
            additional_claims={
                'email': user['email'],
                'role': user['role']
            }
        )
        
        return jsonify({
            'token': access_token,
            'user': {
                'id': str(user['_id']),
                'name': user['name'],
                'email': user['email'],
                'role': user['role']
            }
        })
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/reports', methods=['POST'])
@jwt_required()
def create_report():
    try:
        user_id = get_jwt_identity()
        
        # Handle file upload
        image_url = None
        if 'image' in request.files:
            file = request.files['image']
            if file and file.filename and allowed_file(file.filename):
                filename = str(uuid.uuid4()) + '_' + secure_filename(file.filename)
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
                image_url = f'/uploads/{filename}'
        
        # Get form data
        category = request.form.get('category')
        description = request.form.get('description')
        location_str = request.form.get('location')
        
        if not category or not description:
            return jsonify({'error': 'Category and description are required'}), 400
        
        # Parse location if provided
        location = None
        if location_str:
            import json
            try:
                location = json.loads(location_str)
            except:
                pass
        
        # Create report document
        report_doc = {
            'user_id': ObjectId(user_id),
            'category': category,
            'description': description,
            'image_url': image_url,
            'location': location,
            'status': 'pending',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        # Insert report
        result = mongo.db.reports.insert_one(report_doc)
        report_doc['_id'] = str(result.inserted_id)
        report_doc['user_id'] = str(report_doc['user_id'])
        
        # Emit real-time update
        socketio.emit('new_report', serialize_doc(report_doc), broadcast=True)
        
        # Emit real-time update
        socketio.emit('new_report', serialize_doc(report_doc), broadcast=True)
        
        # Emit real-time update
        socketio.emit('new_report', serialize_doc(report_doc), broadcast=True)
        
        return jsonify(serialize_doc(report_doc)), 201
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/reports/public', methods=['GET'])
def get_public_reports():
    try:
        # Get all reports with user information for public view
        pipeline = [
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'user_id',
                    'foreignField': '_id',
                    'as': 'user'
                }
            },
            {
                '$unwind': '$user'
            },
            {
                '$sort': {'created_at': -1}
            }
        ]
        
        reports = list(mongo.db.reports.aggregate(pipeline))
        
        # Serialize and format reports
        formatted_reports = []
        for report in reports:
            formatted_report = serialize_doc(report)
            formatted_report['user_id'] = str(formatted_report['user_id'])
            formatted_report['userName'] = report['user']['name']
            # Don't expose email in public view
            formatted_reports.append(formatted_report)
        
        return jsonify(formatted_reports)
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/reports/solved', methods=['GET'])
def get_solved_reports():
    try:
        # Get only resolved reports with user information
        pipeline = [
            {
                '$match': {'status': 'resolved'}
            },
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'user_id',
                    'foreignField': '_id',
                    'as': 'user'
                }
            },
            {
                '$unwind': '$user'
            },
            {
                '$sort': {'updated_at': -1}
            }
        ]
        
        reports = list(mongo.db.reports.aggregate(pipeline))
        
        # Serialize and format reports
        formatted_reports = []
        for report in reports:
            formatted_report = serialize_doc(report)
            formatted_report['user_id'] = str(formatted_report['user_id'])
            formatted_report['userName'] = report['user']['name']
            formatted_reports.append(formatted_report)
        
        return jsonify(formatted_reports)
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    try:
        total_reports = mongo.db.reports.count_documents({})
        pending_reports = mongo.db.reports.count_documents({'status': 'pending'})
        in_progress_reports = mongo.db.reports.count_documents({'status': 'in-progress'})
        resolved_reports = mongo.db.reports.count_documents({'status': 'resolved'})
        
        # Category-wise stats
        category_pipeline = [
            {
                '$group': {
                    '_id': '$category',
                    'count': {'$sum': 1}
                }
            },
            {
                '$sort': {'count': -1}
            }
        ]
        
        category_stats = list(mongo.db.reports.aggregate(category_pipeline))
        
        return jsonify({
            'total': total_reports,
            'pending': pending_reports,
            'in_progress': in_progress_reports,
            'resolved': resolved_reports,
            'categories': category_stats
        })
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500
@app.route('/api/reports/public', methods=['GET'])
def get_public_reports():
    try:
        # Get all reports with user information for public view
        pipeline = [
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'user_id',
                    'foreignField': '_id',
                    'as': 'user'
                }
            },
            {
                '$unwind': '$user'
            },
            {
                '$sort': {'created_at': -1}
            }
        ]
        
        reports = list(mongo.db.reports.aggregate(pipeline))
        
        # Serialize and format reports
        formatted_reports = []
        for report in reports:
            formatted_report = serialize_doc(report)
            formatted_report['user_id'] = str(formatted_report['user_id'])
            formatted_report['userName'] = report['user']['name']
            # Don't expose email in public view
            formatted_reports.append(formatted_report)
        
        return jsonify(formatted_reports)
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/reports/solved', methods=['GET'])
def get_solved_reports():
    try:
        # Get only resolved reports with user information
        pipeline = [
            {
                '$match': {'status': 'resolved'}
            },
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'user_id',
                    'foreignField': '_id',
                    'as': 'user'
                }
            },
            {
                '$unwind': '$user'
            },
            {
                '$sort': {'updated_at': -1}
            }
        ]
        
        reports = list(mongo.db.reports.aggregate(pipeline))
        
        # Serialize and format reports
        formatted_reports = []
        for report in reports:
            formatted_report = serialize_doc(report)
            formatted_report['user_id'] = str(formatted_report['user_id'])
            formatted_report['userName'] = report['user']['name']
            formatted_reports.append(formatted_report)
        
        return jsonify(formatted_reports)
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    try:
        total_reports = mongo.db.reports.count_documents({})
        pending_reports = mongo.db.reports.count_documents({'status': 'pending'})
        in_progress_reports = mongo.db.reports.count_documents({'status': 'in-progress'})
        resolved_reports = mongo.db.reports.count_documents({'status': 'resolved'})
        
        # Category-wise stats
        category_pipeline = [
            {
                '$group': {
                    '_id': '$category',
                    'count': {'$sum': 1}
                }
            },
            {
                '$sort': {'count': -1}
            }
        ]
        
        category_stats = list(mongo.db.reports.aggregate(category_pipeline))
        
        return jsonify({
            'total': total_reports,
            'pending': pending_reports,
            'in_progress': in_progress_reports,
            'resolved': resolved_reports,
            'categories': category_stats
        })
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500
@app.route('/api/reports/public', methods=['GET'])
def get_public_reports():
    try:
        # Get all reports with user information for public view
        pipeline = [
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'user_id',
                    'foreignField': '_id',
                    'as': 'user'
                }
            },
            {
                '$unwind': '$user'
            },
            {
                '$sort': {'created_at': -1}
            }
        ]
        
        reports = list(mongo.db.reports.aggregate(pipeline))
        
        # Serialize and format reports
        formatted_reports = []
        for report in reports:
            formatted_report = serialize_doc(report)
            formatted_report['user_id'] = str(formatted_report['user_id'])
            formatted_report['userName'] = report['user']['name']
            # Don't expose email in public view
            formatted_reports.append(formatted_report)
        
        return jsonify(formatted_reports)
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/reports/solved', methods=['GET'])
def get_solved_reports():
    try:
        # Get only resolved reports with user information
        pipeline = [
            {
                '$match': {'status': 'resolved'}
            },
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'user_id',
                    'foreignField': '_id',
                    'as': 'user'
                }
            },
            {
                '$unwind': '$user'
            },
            {
                '$sort': {'updated_at': -1}
            }
        ]
        
        reports = list(mongo.db.reports.aggregate(pipeline))
        
        # Serialize and format reports
        formatted_reports = []
        for report in reports:
            formatted_report = serialize_doc(report)
            formatted_report['user_id'] = str(formatted_report['user_id'])
            formatted_report['userName'] = report['user']['name']
            formatted_reports.append(formatted_report)
        
        return jsonify(formatted_reports)
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    try:
        total_reports = mongo.db.reports.count_documents({})
        pending_reports = mongo.db.reports.count_documents({'status': 'pending'})
        in_progress_reports = mongo.db.reports.count_documents({'status': 'in-progress'})
        resolved_reports = mongo.db.reports.count_documents({'status': 'resolved'})
        
        # Category-wise stats
        category_pipeline = [
            {
                '$group': {
                    '_id': '$category',
                    'count': {'$sum': 1}
                }
            },
            {
                '$sort': {'count': -1}
            }
        ]
        
        category_stats = list(mongo.db.reports.aggregate(category_pipeline))
        
        return jsonify({
            'total': total_reports,
            'pending': pending_reports,
            'in_progress': in_progress_reports,
            'resolved': resolved_reports,
            'categories': category_stats
        })
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500
@app.route('/api/myreports', methods=['GET'])
@jwt_required()
def get_my_reports():
    try:
        user_id = get_jwt_identity()
        
        # Find user's reports
        reports = list(mongo.db.reports.find({'user_id': ObjectId(user_id)}).sort('created_at', -1))
        
        # Serialize reports
        serialized_reports = [serialize_doc(report) for report in reports]
        for report in serialized_reports:
            report['user_id'] = str(report['user_id'])
        
        return jsonify(serialized_reports)
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/admin/reports', methods=['GET'])
@jwt_required()
def get_all_reports():
    try:
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        
        if claims.get('role') != 'admin':
            return jsonify({'error': 'Admin access required'}), 403
        
        # Aggregate reports with user information
        pipeline = [
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'user_id',
                    'foreignField': '_id',
                    'as': 'user'
                }
            },
            {
                '$unwind': '$user'
            },
            {
                '$sort': {'created_at': -1}
            }
        ]
        
        reports = list(mongo.db.reports.aggregate(pipeline))
        
        # Serialize and format reports
        formatted_reports = []
        for report in reports:
            formatted_report = serialize_doc(report)
            formatted_report['user_id'] = str(formatted_report['user_id'])
            formatted_report['userName'] = report['user']['name']
            formatted_report['userEmail'] = report['user']['email']
            formatted_reports.append(formatted_report)
        
        return jsonify(formatted_reports)
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/admin/reports/<report_id>', methods=['PUT'])
@jwt_required()
def update_report_status(report_id):
    try:
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        
        if claims.get('role') != 'admin':
            return jsonify({'error': 'Admin access required'}), 403
        
        data = request.get_json()
        status = data.get('status')
        
        if status not in ['pending', 'in-progress', 'resolved']:
            return jsonify({'error': 'Invalid status'}), 400
        
        # Update report
        result = mongo.db.reports.update_one(
            {'_id': ObjectId(report_id)},
            {
                '$set': {
                    'status': status,
                    'updated_at': datetime.utcnow()
                }
            }
        )
        
        if result.matched_count == 0:
            return jsonify({'error': 'Report not found'}), 404
        
        # Return updated report
        updated_report = mongo.db.reports.find_one({'_id': ObjectId(report_id)})
        serialized_report = serialize_doc(updated_report)
        
        # Emit real-time update
        socketio.emit('report_updated', serialized_report, broadcast=True)
        
        serialized_report = serialize_doc(updated_report)
        
        # Emit real-time update
        socketio.emit('report_updated', serialized_report, broadcast=True)
        
        serialized_report = serialize_doc(updated_report)
        
        # Emit real-time update
        socketio.emit('report_updated', serialized_report, broadcast=True)
        
        return jsonify(serialized_report)
        
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

# Socket.IO events for real-time updates
@socketio.on('connect')
def handle_connect():
    print('Client connected')
    emit('connected', {'data': 'Connected to server'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')
# Socket.IO events for real-time updates
@socketio.on('connect')
def handle_connect():
    print('Client connected')
    emit('connected', {'data': 'Connected to server'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')
# Socket.IO events for real-time updates
@socketio.on('connect')
def handle_connect():
    print('Client connected')
    emit('connected', {'data': 'Connected to server'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()})

if __name__ == '__main__':
    socketio.run(app, debug=True, port=5000, host='0.0.0.0')
import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Shield, LogOut, User, Settings } from 'lucide-react';

const Navbar: React.FC = () => {
  const { user, logout, isAuthenticated, isAdmin } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <nav className="navbar">
      <div className="container">
        <div className="flex justify-between items-center">
          <Link to="/" className="navbar-brand">
            <Shield size={32} />
            <span>CivicReport</span>
          </Link>

          <div className="flex items-center gap-4">
            {isAuthenticated ? (
              <>
                <Link to="/" className="nav-link">
                  Home
                </Link>
                <Link to="/" className="nav-link">
                  Home
                </Link>
                <Link to="/" className="nav-link">
                  Home
                </Link>
                <Link to="/dashboard" className="nav-link">
                  Dashboard
                </Link>
                <Link to="/issues" className="nav-link">
                  All Issues
                </Link>
                <Link to="/solved" className="nav-link">
                  Solved Issues
                </Link>
                <Link to="/issues" className="nav-link">
                  All Issues
                </Link>
                <Link to="/solved" className="nav-link">
                  Solved Issues
                </Link>
                <Link to="/issues" className="nav-link">
                  All Issues
                </Link>
                <Link to="/solved" className="nav-link">
                  Solved Issues
                </Link>
                <Link to="/report" className="btn btn-primary">
                  Report Issue
                </Link>
                {isAdmin && (
                  <Link to="/admin" className="nav-link flex items-center gap-1">
                    <Settings size={16} />
                    <span>Admin</span>
                  </Link>
                )}
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <User size={16} />
                    <span>{user?.name}</span>
                  </div>
                  <button
                    onClick={handleLogout}
                    className="text-gray-500 hover:text-red-600 transition-colors"
                    title="Logout"
                  >
                    <LogOut size={20} />
                  </button>
                </div>
              </>
            ) : (
              <>
                <Link to="/login" className="nav-link">
                  Login
                </Link>
                <Link to="/register" className="btn btn-primary">
                  Register
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
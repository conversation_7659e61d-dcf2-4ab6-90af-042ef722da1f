import React, { useState, useEffect } from 'react';
import { Calendar, MapPin, Camera, User, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import io from 'socket.io-client';

interface Report {
  _id: string;
  category: string;
  description: string;
  image_url?: string;
  location?: { latitude: number; longitude: number };
  status: 'pending' | 'in-progress' | 'resolved';
  created_at: string;
  updated_at: string;
  userName: string;
}

const socket = io('http://localhost:5000');

const GeneralIssues: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    'Road Maintenance',
    'Streetlight Issues',
    'Waste Management',
    'Water Problems',
    'Park Maintenance',
    'Noise Complaints',
    'Building Violations',
    'Other',
  ];

  useEffect(() => {
    fetchReports();
    
    // Real-time updates
    socket.on('new_report', (newReport) => {
      setReports(prev => [newReport, ...prev]);
    });
    
    socket.on('report_updated', (updatedReport) => {
      setReports(prev => prev.map(report => 
        report._id === updatedReport._id ? updatedReport : report
      ));
    });
    
    return () => {
      socket.off('new_report');
      socket.off('report_updated');
    };
  }, []);

  const fetchReports = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/reports/public');
      
      if (!response.ok) {
        throw new Error('Failed to fetch reports');
      }

      const data = await response.json();
      setReports(data);
    } catch (err: any) {
      setError(err.message || 'Failed to load reports');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'in-progress':
        return <AlertCircle className="h-4 w-4" />;
      case 'resolved':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const filteredReports = reports.filter(report => {
    if (selectedCategory === 'all') return true;
    return report.category === selectedCategory;
  });

  const groupedReports = categories.reduce((acc, category) => {
    acc[category] = filteredReports.filter(report => report.category === category);
    return acc;
  }, {} as Record<string, Report[]>);

  if (loading) {
    return (
      <div className="container py-8">
        <div className="loading">
          <div className="spinner"></div>
          <p>Loading community issues...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Community Issues</h1>
        <p className="text-gray-600">
          View all reported issues in your community and track their progress
        </p>
      </div>

      {error && (
        <div className="alert alert-error mb-6">
          <AlertCircle className="h-5 w-5" />
          <span>{error}</span>
        </div>
      )}

      {/* Category Filter */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedCategory === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            All Issues ({reports.length})
          </button>
          {categories.map((category) => {
            const count = reports.filter(r => r.category === category).length;
            return (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {category} ({count})
              </button>
            );
          })}
        </div>
      </div>

      {/* Issues by Category */}
      {selectedCategory === 'all' ? (
        <div className="space-y-12">
          {categories.map((category) => {
            const categoryReports = groupedReports[category];
            if (categoryReports.length === 0) return null;

            return (
              <div key={category} className="category-section">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                  {category}
                  <span className="bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded-full">
                    {categoryReports.length}
                  </span>
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {categoryReports.map((report) => (
                    <ReportCard key={report._id} report={report} />
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredReports.map((report) => (
            <ReportCard key={report._id} report={report} />
          ))}
        </div>
      )}

      {filteredReports.length === 0 && (
        <div className="text-center py-12">
          <MapPin className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No issues found</h3>
          <p className="text-gray-500">
            {selectedCategory === 'all' 
              ? 'No issues have been reported yet' 
              : `No issues found in ${selectedCategory} category`}
          </p>
        </div>
      )}
    </div>
  );
};

const ReportCard: React.FC<{ report: Report }> = ({ report }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'in-progress':
        return <AlertCircle className="h-4 w-4" />;
      case 'resolved':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <div className="card hover:shadow-lg transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-lg font-semibold text-gray-900">{report.category}</h3>
            <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${getStatusColor(report.status)}`}>
              {getStatusIcon(report.status)}
              <span className="capitalize">{report.status.replace('-', ' ')}</span>
            </span>
          </div>
          <p className="text-gray-700 mb-4 line-clamp-3">{report.description}</p>
          
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <div className="flex items-center gap-1">
              <User className="h-4 w-4" />
              <span>{report.userName}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>{new Date(report.created_at).toLocaleDateString()}</span>
            </div>
            {report.location && (
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                <span>Located</span>
              </div>
            )}
            {report.image_url && (
              <div className="flex items-center gap-1">
                <Camera className="h-4 w-4" />
                <span>Photo</span>
              </div>
            )}
          </div>
        </div>

        {report.image_url && (
          <div className="ml-4">
            <img
              src={`http://localhost:5000${report.image_url}`}
              alt="Report"
              className="w-20 h-20 object-cover rounded-lg"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default GeneralIssues;
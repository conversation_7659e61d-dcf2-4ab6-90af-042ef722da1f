#!/bin/bash

# Civic Issue Reporting System - Startup Script

echo "🚀 Starting Civic Issue Reporting System..."

# Check if MongoDB is running
if ! pgrep -x "mongod" > /dev/null; then
    echo "⚠️  MongoDB is not running. Please start MongoDB first:"
    echo "   sudo systemctl start mongod"
    echo "   or"
    echo "   brew services start mongodb/brew/mongodb-community"
    exit 1
fi

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+"
    exit 1
fi

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+"
    exit 1
fi

# Install Python dependencies
echo "📦 Installing Python dependencies..."
cd backend
if [ ! -f "requirements.txt" ]; then
    echo "❌ requirements.txt not found in backend directory"
    exit 1
fi

python3 -m pip install -r requirements.txt

# Create uploads directory
mkdir -p uploads

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
cd ../frontend
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found in frontend directory"
    exit 1
fi

npm install

# Start backend server in background
echo "🔧 Starting Flask backend server..."
cd ../backend
python3 app.py &
BACKEND_PID=$!

# Wait for backend to start
sleep 3

# Start frontend development server
echo "🎨 Starting React frontend server..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!

echo "✅ System started successfully!"
echo "📱 Frontend: http://localhost:5173"
echo "🔧 Backend API: http://localhost:5000"
echo ""
echo "Demo Accounts:"
echo "👤 Admin: <EMAIL> / demo123"
echo "👤 User: <EMAIL> / demo123"
echo ""
echo "Press Ctrl+C to stop all servers"

# Wait for user interrupt
trap "echo '🛑 Stopping servers...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
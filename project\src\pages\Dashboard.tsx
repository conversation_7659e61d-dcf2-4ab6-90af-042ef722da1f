import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { FileText, MapPin, TrendingUp, Settings } from 'lucide-react';

const Dashboard: React.FC = () => {
  const { user, isAdmin } = useAuth();

  const quickActions = [
    {
      title: 'Report New Issue',
      description: 'Submit a new civic issue in your area',
      icon: <FileText className="h-8 w-8 text-blue-600" />,
      link: '/report',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'My Reports',
      description: 'View your submitted reports and their status',
      icon: <MapPin className="h-8 w-8 text-green-600" />,
      link: '/my-reports',
      bgColor: 'bg-green-50',
    },
  ];

  const adminActions = [
    {
      title: 'Admin Dashboard',
      description: 'Manage all reports and user activities',
      icon: <Settings className="h-8 w-8 text-purple-600" />,
      link: '/admin',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="mt-2 text-gray-600">
          Manage your civic reports and help improve your community
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {quickActions.map((action, index) => (
          <Link
            key={index}
            to={action.link}
            className="block p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow"
          >
            <div className={`inline-flex p-3 rounded-lg ${action.bgColor} mb-4`}>
              {action.icon}
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {action.title}
            </h3>
            <p className="text-gray-600">
              {action.description}
            </p>
          </Link>
        ))}

        {isAdmin && adminActions.map((action, index) => (
          <Link
            key={`admin-${index}`}
            to={action.link}
            className="block p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow"
          >
            <div className={`inline-flex p-3 rounded-lg ${action.bgColor} mb-4`}>
              {action.icon}
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {action.title}
            </h3>
            <p className="text-gray-600">
              {action.description}
            </p>
          </Link>
        ))}
      </div>

      {/* Statistics */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Your Impact
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 mb-2">--</div>
            <div className="text-sm text-gray-600">Reports Submitted</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 mb-2">--</div>
            <div className="text-sm text-gray-600">Issues Resolved</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600 mb-2">--</div>
            <div className="text-sm text-gray-600">Community Rank</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
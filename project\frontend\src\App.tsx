import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Navbar from './components/Navbar';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import ReportIssue from './pages/ReportIssue';
import MyReports from './pages/MyReports';
import AdminDashboard from './pages/AdminDashboard';
import GeneralIssues from './pages/GeneralIssues';
import SolvedIssues from './pages/SolvedIssues';
import GeneralIssues from './pages/GeneralIssues';
import SolvedIssues from './pages/SolvedIssues';
import GeneralIssues from './pages/GeneralIssues';
import SolvedIssues from './pages/SolvedIssues';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/issues" element={<GeneralIssues />} />
            <Route path="/solved" element={<SolvedIssues />} />
            <Route path="/issues" element={<GeneralIssues />} />
            <Route path="/solved" element={<SolvedIssues />} />
            <Route path="/issues" element={<GeneralIssues />} />
            <Route path="/solved" element={<SolvedIssues />} />
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/report"
              element={
                <ProtectedRoute>
                  <ReportIssue />
                </ProtectedRoute>
              }
            />
            <Route
              path="/my-reports"
              element={
                <ProtectedRoute>
                  <MyReports />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin"
              element={
                <ProtectedRoute requiredRole="admin">
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { FileText, MapPin, TrendingUp, Settings, Users, CheckCircle } from 'lucide-react';
import io from 'socket.io-client';

const socket = io('http://localhost:5000');

const Dashboard: React.FC = () => {
  const { user, isAdmin } = useAuth();
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    in_progress: 0,
    resolved: 0,
    categories: [] as any[],
  });
  const [userStats, setUserStats] = useState({
    total: 0,
    pending: 0,
    in_progress: 0,
    resolved: 0,
  });
  const [loading, setLoading] = useState(true);
  const [resolvedNotice, setResolvedNotice] = useState<{ id: string; category?: string } | null>(null);

  useEffect(() => {
    fetchStats();
    fetchUserStats();

    socket.on('new_report', () => {
      fetchStats();
      fetchUserStats();
    });

    socket.on('report_updated', (updatedReport: any) => {
      fetchStats();
      fetchUserStats();
      try {
        if (
          updatedReport &&
          updatedReport.status === 'resolved' &&
          updatedReport.user_id &&
          user?.id &&
          String(updatedReport.user_id) === String(user.id)
        ) {
          setResolvedNotice({ id: updatedReport._id || updatedReport.id, category: updatedReport.category });
        }
      } catch {}
    });

    return () => {
      socket.off('new_report');
      socket.off('report_updated');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const fetchUserStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5000/api/myreports', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const reports = await response.json();
        const userStatsData = {
          total: reports.length,
          pending: reports.filter((r: any) => r.status === 'pending').length,
          in_progress: reports.filter((r: any) => r.status === 'in-progress').length,
          resolved: reports.filter((r: any) => r.status === 'resolved').length,
        };
        setUserStats(userStatsData);
      }
    } catch (error) {
      console.error('Failed to fetch user stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: 'Report Issue',
      description: 'Submit a new community issue',
      icon: FileText,
      href: '/report',
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      title: 'View Map',
      description: 'Explore issues in your area',
      icon: MapPin,
      href: '/map',
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      title: 'Analytics',
      description: 'View community trends',
      icon: TrendingUp,
      href: '/analytics',
      color: 'bg-purple-500 hover:bg-purple-600',
    },
  ];

  if (isAdmin) {
    quickActions.push({
      title: 'Admin Panel',
      description: 'Manage reports and users',
      icon: Settings,
      href: '/admin',
      color: 'bg-red-500 hover:bg-red-600',
    });
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {resolvedNotice && (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4 flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <div className="text-sm font-medium text-green-800">Your issue has been resolved</div>
              <div className="text-sm text-green-700">{resolvedNotice.category ? `${resolvedNotice.category} ` : ''}report is marked as resolved. Check it in My Reports.</div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Link to="/my-reports" className="text-sm font-medium text-green-700 hover:text-green-800 underline">View</Link>
            <button onClick={() => setResolvedNotice(null)} className="text-sm text-green-700 hover:text-green-900">Dismiss</button>
          </div>
        </div>
      )}

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="mt-2 text-gray-600">
          Here's what's happening in your community today.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickActions.map((action) => {
          const Icon = action.icon;
          return (
            <Link
              key={action.title}
              to={action.href}
              className={`${action.color} text-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105`}
            >
              <Icon className="h-8 w-8 mb-4" />
              <h3 className="text-lg font-semibold mb-2">{action.title}</h3>
              <p className="text-sm opacity-90">{action.description}</p>
            </Link>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Your Impact</h2>
          {loading ? (
            <div className="loading">
              <div className="spinner"></div>
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 mb-2">{userStats.total}</div>
                <div className="text-sm text-gray-600">Reports Submitted</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600 mb-2">{userStats.pending}</div>
                <div className="text-sm text-gray-600">Pending</div>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 mb-2">{userStats.in_progress}</div>
                <div className="text-sm text-gray-600">In Progress</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600 mb-2">{userStats.resolved}</div>
                <div className="text-sm text-gray-600">Resolved</div>
              </div>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Community Overview</h2>
          {loading ? (
            <div className="loading">
              <div className="spinner"></div>
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-600 mb-2">{stats.total}</div>
                <div className="text-sm text-gray-600">Total Issues</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600 mb-2">{stats.pending}</div>
                <div className="text-sm text-gray-600">Pending</div>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 mb-2">{stats.in_progress}</div>
                <div className="text-sm text-gray-600">In Progress</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600 mb-2">{stats.resolved}</div>
                <div className="text-sm text-gray-600">Resolved</div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Links</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link to="/issues" className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
            <Users className="h-6 w-6 text-blue-600" />
            <div>
              <div className="font-medium text-gray-900">All Issues</div>
              <div className="text-sm text-gray-600">View community reports</div>
            </div>
          </Link>
          <Link to="/solved" className="flex items-center gap-3 p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
            <CheckCircle className="h-6 w-6 text-green-600" />
            <div>
              <div className="font-medium text-gray-900">Solved Issues</div>
              <div className="text-sm text-gray-600">Celebrate success stories</div>
            </div>
          </Link>
          <Link to="/my-reports" className="flex items-center gap-3 p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
            <FileText className="h-6 w-6 text-purple-600" />
            <div>
              <div className="font-medium text-gray-900">My Reports</div>
              <div className="text-sm text-gray-600">Track your submissions</div>
            </div>
          </Link>
        </div>
      </div>

      {stats.categories.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6 mt-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Most Reported Issues</h2>
          <div className="space-y-3">
            {stats.categories.slice(0, 5).map((category: any, index: number) => (
              <div key={category._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">{index + 1}</div>
                  <span className="font-medium text-gray-900">{category._id}</span>
                </div>
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-medium">{category.count} reports</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
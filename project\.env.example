# Flask Configuration
SECRET_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoidXNlcjEyMyIsImV4cCI6MTc1Nzk0NzY4OH0.RGznrAvuzZzMNaNHV8-wD93Ti53jPwNuZyW5p00slyU
FLASK_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017

# Email Configuration (Optional - for production)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# CORS Configuration
FRONTEND_URL=http://localhost:3000
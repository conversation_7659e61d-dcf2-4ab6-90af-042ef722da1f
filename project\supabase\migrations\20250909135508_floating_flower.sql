-- Civic Issue Reporting System Database Schema
-- This SQL schema is provided for reference and potential PostgreSQL migration
-- The actual implementation uses MongoDB with the collections defined below

-- Users table equivalent (MongoDB: users collection)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'citizen' CHECK (role IN ('citizen', 'admin')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reports table equivalent (MongoDB: reports collection)
CREATE TABLE IF NOT EXISTS reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    image_url VARCHAR(500),
    location_latitude DECIMAL(10, 8),
    location_longitude DECIMAL(11, 8),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in-progress', 'resolved')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_reports_user_id ON reports(user_id);
CREATE INDEX IF NOT EXISTS idx_reports_status ON reports(status);
CREATE INDEX IF NOT EXISTS idx_reports_category ON reports(category);
CREATE INDEX IF NOT EXISTS idx_reports_created_at ON reports(created_at);

-- MongoDB Collections Structure (for reference):
/*
users: {
    _id: ObjectId,
    name: String,
    email: String (unique),
    password: String (hashed),
    role: String (citizen/admin),
    created_at: Date
}

reports: {
    _id: ObjectId,
    user_id: ObjectId (ref to users),
    category: String,
    description: String,
    image_url: String (optional),
    location: {
        latitude: Number,
        longitude: Number
    } (optional),
    status: String (pending/in-progress/resolved),
    created_at: Date,
    updated_at: Date
}
*/

-- Sample data for testing
INSERT INTO users (name, email, password_hash, role) VALUES 
('Admin User', '<EMAIL>', '$2b$12$example_hash_here', 'admin'),
('John Citizen', '<EMAIL>', '$2b$12$example_hash_here', 'citizen');

INSERT INTO reports (user_id, category, description, status) VALUES 
((SELECT id FROM users WHERE email = '<EMAIL>'), 'Road Maintenance', 'Large pothole on Main Street causing traffic issues', 'pending'),
((SELECT id FROM users WHERE email = '<EMAIL>'), 'Streetlight Issues', 'Broken streetlight at Park Avenue intersection', 'in-progress');
import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { MapPin, Camera, Bell, Users } from 'lucide-react';

const Home: React.FC = () => {
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: <MapPin className="h-8 w-8 text-blue-600" />,
      title: 'Location-Based Reporting',
      description: 'Automatically capture and report issues at your current location',
    },
    {
      icon: <Camera className="h-8 w-8 text-green-600" />,
      title: 'Photo Evidence',
      description: 'Upload photos to document civic issues clearly',
    },
    {
      icon: <Bell className="h-8 w-8 text-yellow-600" />,
      title: 'Status Updates',
      description: 'Get notified when your reported issues are being addressed',
    },
    {
      icon: <Users className="h-8 w-8 text-purple-600" />,
      title: 'Community Impact',
      description: 'Work together with local authorities to improve your community',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Make Your Voice Heard
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Report civic issues and help build a better community
            </p>
            {!isAuthenticated ? (
              <div className="space-x-4">
                <Link
                  to="/register"
                  className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg text-lg font-semibold transition-colors"
                >
                  Get Started
                </Link>
                <Link
                  to="/login"
                  className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3 rounded-lg text-lg font-semibold transition-colors"
                >
                  Sign In
                </Link>
              </div>
            ) : (
              <Link
                to="/report"
                className="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors"
              >
                Report an Issue
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-600">
              Simple, effective civic engagement in three easy steps
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                <div className="flex justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gray-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Ready to Make a Difference?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of citizens working to improve their communities
          </p>
          {!isAuthenticated && (
            <Link
              to="/register"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors"
            >
              Sign Up Today
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Home;
import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { MapPin, Camera, Bell, Users } from 'lucide-react';

const Home: React.FC = () => {
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: <MapPin size={32} className="text-blue-600" />,
      title: 'Location-Based Reporting',
      description: 'Automatically capture and report issues at your current location',
    },
    {
      icon: <Camera size={32} className="text-green-600" />,
      title: 'Photo Evidence',
      description: 'Upload photos to document civic issues clearly',
    },
    {
      icon: <Bell size={32} className="text-yellow-600" />,
      title: 'Status Updates',
      description: 'Get notified when your reported issues are being addressed',
    },
    {
      icon: <Users size={32} className="text-purple-600" />,
      title: 'Community Impact',
      description: 'Work together with local authorities to improve your community',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-20">
        <div className="container">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Make Your Voice Heard
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Report civic issues and help build a better community
            </p>
            {!isAuthenticated ? (
              <div className="flex gap-4 justify-center">
                <Link to="/register" className="btn btn-primary">
                  Get Started
                </Link>
                <Link to="/login" className="btn btn-outline">
                  Sign In
                </Link>
              </div>
            ) : (
              <Link to="/report" className="btn btn-success">
                Report an Issue
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="section">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-600">
              Simple, effective civic engagement in three easy steps
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="card text-center">
                <div className="flex justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gray-50 py-20">
        <div className="container text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Ready to Make a Difference?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of citizens working to improve their communities
          </p>
          {!isAuthenticated && (
            <Link to="/register" className="btn btn-primary">
              Sign Up Today
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Home;
import express from 'express';
import cors from 'cors';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3001;
const JWT_SECRET = 'civic_reporting_secret_key_2024';

// Middleware
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// File storage setup
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, 'uploads');
    try {
      await fs.access(uploadDir);
    } catch {
      await fs.mkdir(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({ storage });

// Database simulation with JSON files
const DB_PATH = path.join(__dirname, 'data');

async function initDatabase() {
  try {
    await fs.access(DB_PATH);
  } catch {
    await fs.mkdir(DB_PATH, { recursive: true });
  }
  
  const usersFile = path.join(DB_PATH, 'users.json');
  const reportsFile = path.join(DB_PATH, 'reports.json');
  
  try {
    await fs.access(usersFile);
  } catch {
    await fs.writeFile(usersFile, JSON.stringify([]));
  }
  
  try {
    await fs.access(reportsFile);
  } catch {
    await fs.writeFile(reportsFile, JSON.stringify([]));
  }
}

async function readUsers() {
  const data = await fs.readFile(path.join(DB_PATH, 'users.json'), 'utf-8');
  return JSON.parse(data);
}

async function writeUsers(users) {
  await fs.writeFile(path.join(DB_PATH, 'users.json'), JSON.stringify(users, null, 2));
}

async function readReports() {
  const data = await fs.readFile(path.join(DB_PATH, 'reports.json'), 'utf-8');
  return JSON.parse(data);
}

async function writeReports(reports) {
  await fs.writeFile(path.join(DB_PATH, 'reports.json'), JSON.stringify(reports, null, 2));
}

// Auth middleware
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
}

// Routes
app.post('/api/register', async (req, res) => {
  try {
    const { name, email, password, role = 'citizen' } = req.body;
    
    if (!name || !email || !password) {
      return res.status(400).json({ error: 'All fields are required' });
    }

    const users = await readUsers();
    
    if (users.find(user => user.email === email)) {
      return res.status(400).json({ error: 'Email already registered' });
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = {
      id: uuidv4(),
      name,
      email,
      password: hashedPassword,
      role,
      createdAt: new Date().toISOString()
    };

    users.push(newUser);
    await writeUsers(users);

    const token = jwt.sign(
      { id: newUser.id, email: newUser.email, role: newUser.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.status(201).json({
      token,
      user: { id: newUser.id, name: newUser.name, email: newUser.email, role: newUser.role }
    });
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

app.post('/api/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }

    const users = await readUsers();
    const user = users.find(u => u.email === email);
    
    if (!user) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }

    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      token,
      user: { id: user.id, name: user.name, email: user.email, role: user.role }
    });
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

app.post('/api/reports', authenticateToken, upload.single('image'), async (req, res) => {
  try {
    const { category, description, location } = req.body;
    const userId = req.user.id;

    if (!category || !description) {
      return res.status(400).json({ error: 'Category and description are required' });
    }

    const reports = await readReports();
    const newReport = {
      id: uuidv4(),
      userId,
      category,
      description,
      imageUrl: req.file ? `/uploads/${req.file.filename}` : null,
      location: location ? JSON.parse(location) : null,
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    reports.push(newReport);
    await writeReports(reports);

    res.status(201).json(newReport);
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

app.get('/api/myreports', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const reports = await readReports();
    const userReports = reports.filter(report => report.userId === userId);
    
    res.json(userReports);
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

app.get('/api/admin/reports', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const reports = await readReports();
    const users = await readUsers();

    const reportsWithUsers = reports.map(report => {
      const user = users.find(u => u.id === report.userId);
      return {
        ...report,
        userName: user ? user.name : 'Unknown',
        userEmail: user ? user.email : 'Unknown'
      };
    });

    res.json(reportsWithUsers);
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

app.put('/api/admin/reports/:id', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { id } = req.params;
    const { status } = req.body;

    if (!['pending', 'in-progress', 'resolved'].includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    const reports = await readReports();
    const reportIndex = reports.findIndex(report => report.id === id);

    if (reportIndex === -1) {
      return res.status(404).json({ error: 'Report not found' });
    }

    reports[reportIndex].status = status;
    reports[reportIndex].updatedAt = new Date().toISOString();

    await writeReports(reports);
    res.json(reports[reportIndex]);
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

// Initialize database and start server
initDatabase().then(() => {
  app.listen(PORT, () => {
    console.log(`🚀 Civic Reporting API running on port ${PORT}`);
  });
});
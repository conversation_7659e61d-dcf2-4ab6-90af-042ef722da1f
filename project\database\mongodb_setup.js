// MongoDB Database Setup Script
// Run this script to initialize the MongoDB database with proper indexes and sample data

// Connect to MongoDB
use civic_reporting;

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "role": 1 });
db.users.createIndex({ "created_at": -1 });

db.reports.createIndex({ "user_id": 1 });
db.reports.createIndex({ "status": 1 });
db.reports.createIndex({ "category": 1 });
db.reports.createIndex({ "created_at": -1 });
db.reports.createIndex({ "location": "2dsphere" }); // For geospatial queries

// Sample data insertion
// Note: In production, passwords should be properly hashed using bcrypt

// Insert sample users
db.users.insertMany([
    {
        name: "Admin User",
        email: "<EMAIL>",
        password: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm", // demo123
        role: "admin",
        created_at: new Date()
    },
    {
        name: "John Citizen",
        email: "<EMAIL>",
        password: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm", // demo123
        role: "citizen",
        created_at: new Date()
    }
]);

// Get user IDs for sample reports
const adminUser = db.users.findOne({ email: "<EMAIL>" });
const citizenUser = db.users.findOne({ email: "<EMAIL>" });

// Insert sample reports
db.reports.insertMany([
    {
        user_id: citizenUser._id,
        category: "Road Maintenance",
        description: "Large pothole on Main Street causing traffic issues and potential vehicle damage",
        location: {
            latitude: 40.7128,
            longitude: -74.0060
        },
        status: "pending",
        created_at: new Date(),
        updated_at: new Date()
    },
    {
        user_id: citizenUser._id,
        category: "Streetlight Issues",
        description: "Broken streetlight at Park Avenue intersection creating safety hazard",
        location: {
            latitude: 40.7589,
            longitude: -73.9851
        },
        status: "in-progress",
        created_at: new Date(Date.now() - 86400000), // 1 day ago
        updated_at: new Date()
    },
    {
        user_id: citizenUser._id,
        category: "Waste Management",
        description: "Overflowing garbage bins in Central Park area",
        location: {
            latitude: 40.7829,
            longitude: -73.9654
        },
        status: "resolved",
        created_at: new Date(Date.now() - 172800000), // 2 days ago
        updated_at: new Date(Date.now() - 86400000) // 1 day ago
    }
]);

print("Database setup completed successfully!");
print("Sample users created:");
print("- Admin: <EMAIL> / demo123");
print("- User: <EMAIL> / demo123");
print("Sample reports created with various statuses");
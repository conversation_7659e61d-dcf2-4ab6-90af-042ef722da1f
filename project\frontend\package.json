{"name": "civic-reporting-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.20.1", "lucide-react": "^0.344.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "socket.io-client": "^4.7.2"}, "devDependencies": {"@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "typescript": "^5.5.3", "vite": "^5.4.2"}}
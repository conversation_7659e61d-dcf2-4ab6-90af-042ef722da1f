# Civic Issue Reporting System

A comprehensive Progressive Web Application (PWA) for reporting and managing civic issues in communities. Built with Flask (Python), React.js, and MongoDB.

## 🚀 Features

### User Features
- **Authentication**: Secure registration and login with JWT tokens
- **Issue Reporting**: Submit reports with photos and automatic location capture
- **My Reports**: Track status of submitted issues (Pending/In Progress/Resolved)
- **Progressive Web App**: Installable on mobile devices with offline support

### Admin Features
- **Admin Dashboard**: Comprehensive view of all reported issues
- **Status Management**: Update report statuses and track progress
- **User Management**: View reporter information and contact details
- **Analytics**: Filter and sort reports by category, status, and location

### Technical Features
- **Responsive Design**: Mobile-first approach with clean, modern UI
- **Real-time Updates**: Live status updates and notifications
- **Geolocation**: Automatic location capture using browser APIs
- **File Upload**: Secure image upload and storage
- **Database**: MongoDB for scalable data storage

## 🛠️ Tech Stack

### Backend
- **Flask** - Python web framework
- **MongoDB** - NoSQL database
- **Flask-JWT-Extended** - JWT authentication
- **Flask-PyMongo** - MongoDB integration
- **Flask-CORS** - Cross-origin resource sharing
- **Flask-Bcrypt** - Password hashing

### Frontend
- **React.js** - User interface library
- **TypeScript** - Type-safe JavaScript
- **CSS3** - Custom styling with responsive design
- **Leaflet** - Interactive maps
- **Lucide React** - Modern icon library

### PWA Features
- **Service Worker** - Offline caching and background sync
- **Web App Manifest** - Installable app configuration
- **Push Notifications** - Real-time updates (placeholder)

## 📁 Project Structure

```
civic-reporting-system/
├── backend/
│   ├── app.py                 # Flask application
│   ├── uploads/               # File upload directory
│   └── requirements.txt       # Python dependencies
├── frontend/
│   ├── src/
│   │   ├── components/        # React components
│   │   ├── contexts/          # React contexts
│   │   ├── pages/             # Page components
│   │   ├── styles/            # CSS stylesheets
│   │   └── main.tsx           # Application entry point
│   ├── public/
│   │   ├── manifest.json      # PWA manifest
│   │   └── sw.js              # Service worker
│   └── package.json           # Node.js dependencies
├── database/
│   ├── schema.sql             # SQL schema (reference)
│   └── mongodb_setup.js       # MongoDB initialization
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Python 3.8+
- Node.js 16+
- MongoDB 4.4+

### Backend Setup

1. **Install Python dependencies**:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your MongoDB URI and other settings
   ```

3. **Initialize MongoDB**:
   ```bash
   # Connect to MongoDB and run the setup script
   mongo civic_reporting < ../database/mongodb_setup.js
   ```

4. **Start Flask server**:
   ```bash
   python app.py
   ```
   Server runs on `http://localhost:5000`

### Frontend Setup

1. **Install Node.js dependencies**:
   ```bash
   cd frontend
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```
   Application runs on `http://localhost:5173`

### Database Setup

#### MongoDB Collections

The application uses two main collections:

**users**:
```javascript
{
  _id: ObjectId,
  name: String,
  email: String (unique),
  password: String (hashed),
  role: String (citizen/admin),
  created_at: Date
}
```

**reports**:
```javascript
{
  _id: ObjectId,
  user_id: ObjectId (ref to users),
  category: String,
  description: String,
  image_url: String (optional),
  location: {
    latitude: Number,
    longitude: Number
  } (optional),
  status: String (pending/in-progress/resolved),
  created_at: Date,
  updated_at: Date
}
```

#### Sample Data

The system includes demo accounts:
- **Admin**: <EMAIL> / demo123
- **User**: <EMAIL> / demo123

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the backend directory:

```env
MONGO_URI=mongodb://localhost:27017/civic_reporting
JWT_SECRET_KEY=your-secret-key-here
FLASK_ENV=development
FLASK_DEBUG=True
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=********
```

### MongoDB Indexes

For optimal performance, create these indexes:

```javascript
db.users.createIndex({ "email": 1 }, { unique: true });
db.reports.createIndex({ "user_id": 1 });
db.reports.createIndex({ "status": 1 });
db.reports.createIndex({ "category": 1 });
db.reports.createIndex({ "created_at": -1 });
db.reports.createIndex({ "location": "2dsphere" });
```

## 📱 PWA Features

### Installation
- Visit the app in a modern browser
- Look for the "Install" prompt or use browser menu
- App will be installed as a native-like application

### Offline Support
- Static assets are cached for offline viewing
- Form data is stored locally when offline
- Automatic sync when connection is restored

### Service Worker
- Caches essential resources
- Provides offline fallbacks
- Enables background sync

## 🔐 Security Features

- **Password Hashing**: Bcrypt for secure password storage
- **JWT Authentication**: Stateless authentication tokens
- **CORS Protection**: Configured for secure cross-origin requests
- **File Upload Validation**: Secure file type and size validation
- **Input Sanitization**: Protection against common web vulnerabilities

## 🎨 UI/UX Features

- **Responsive Design**: Mobile-first approach
- **Modern Interface**: Clean, intuitive design
- **Status Indicators**: Visual feedback for report statuses
- **Interactive Maps**: Location visualization with Leaflet
- **Loading States**: Smooth user experience with loading indicators
- **Error Handling**: User-friendly error messages

## 🚀 Deployment

### Production Considerations

1. **Environment Variables**: Set production values
2. **Database**: Use MongoDB Atlas or dedicated server
3. **File Storage**: Consider cloud storage (AWS S3, Google Cloud)
4. **HTTPS**: Enable SSL/TLS certificates
5. **Process Management**: Use Gunicorn or similar WSGI server

### Docker Deployment (Optional)

```dockerfile
# Backend Dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the demo accounts and sample data

## 🔄 Future Enhancements

- Real-time notifications with WebSockets
- Advanced analytics and reporting
- Integration with municipal systems
- Mobile app versions (React Native)
- Multi-language support
- Advanced mapping features
- Automated issue categorization with AI